import { toast } from 'react-toastify';

interface FetchOptions extends RequestInit {
  token?: string; // Optional token for when you're explicitly passing a token
  toastMessage?: string; // Custom message for the toast
  responseType?: 'json' | 'text'; // Specify response type
  suppressToast?: boolean; // New option to control toast display
  contentType?: string;
  deleteContentType?: boolean;
  excludeCredentials?: boolean;
}

const fetchWrapper = async <T>(
  url: string,
  options: FetchOptions = {}
): Promise<T> => {
  const {
    token,
    toastMessage,
    responseType = 'json',
    suppressToast,
    contentType,
    deleteContentType,
    excludeCredentials,
    ...restOptions
  } = options;

  const headers = new Headers(restOptions.headers);

  // Set default Content-Type header if not provided
  if (!headers.has('Content-Type') && !deleteContentType) {
    headers.set('Content-Type', contentType || 'application/json');
  }

  // Delete Content-Type header if specified
  if (deleteContentType) {
    headers.delete('Content-Type');
  }

  // Set credentials to 'include' for cross-origin requests to ensure cookies are sent and received
  const credentials = excludeCredentials ? undefined : 'include';

  // Note: Setting Cookie header manually doesn't work for browser-to-server requests
  // The browser will automatically include cookies in the request if credentials is 'include'
  // This is only useful for server-side requests
  if (token && typeof window === 'undefined') {
    headers.set('Cookie', `accessToken=${token}`);
  }

  try {
    const response = await fetch(url, {
      ...restOptions,
      headers,
      credentials,
    });

    const result =
      responseType === 'json' ? await response.json() : await response.text();

    if (!response.ok) {
      const errorMessage =
        responseType === 'json' && result.message
          ? result.message
          : 'HTTP Error';
      throw new Error(errorMessage);
    }

    return result as T;
  } catch (error) {
    if (typeof window !== 'undefined' && !suppressToast) {
      const errorMessage =
        toastMessage || (error as any)?.message || 'Something went wrong!';
      toast.error(errorMessage);
    }
    throw error;
  }
};

export default fetchWrapper;
