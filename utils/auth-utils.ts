import { BASE_URL } from '@/config';
import fetchWrapper from './fetch-wrapper';
import { setIsUserLogin, setUserDetails } from '@/redux/slices/auth-slice';
import { AppDispatch } from '@/redux/store';

/**
 * Checks if the user is authenticated by calling the auth/check endpoint
 * Updates the Redux store with user details if authenticated
 */
export const checkAuthStatus = async (dispatch: AppDispatch): Promise<boolean> => {
  try {
    const response = await fetchWrapper('/api/proxy/auth/check', {
      method: 'GET',
      suppressToast: true, // Don't show error toasts for auth checks
    });

    if (response) {
      // User is authenticated, update Redux store
      dispatch(setIsUserLogin(true));
      return true;
    }

    // User is not authenticated
    dispatch(setIsUserLogin(false));
    return false;
  } catch (error) {
    console.error('Auth check failed:', error);
    dispatch(setIsUserLogin(false));
    return false;
  }
};

/**
 * Logs the user out by calling the auth/logout endpoint
 * Updates the Redux store to reflect logged out state
 */
export const logoutUser = async (dispatch: AppDispatch): Promise<boolean> => {
  try {
    await fetchWrapper('/api/proxy/auth/logout', {
      method: 'DELETE',
    });

    // Reset auth state
    dispatch(setIsUserLogin(false));
    dispatch(setUserDetails({
      name: '',
      email: '',
      mobile: '',
      avatar: '',
      referralCode: '',
      balance: 0,
      pendingCount: 0,
      confirmedCount: 0,
      personalInterest: [],
      sendNotification: true,
    }));

    return true;
  } catch (error) {
    console.error('Logout failed:', error);
    return false;
  }
};
