'use client';

import { useAppDispatch } from '@/redux/hooks';
import { useEffect } from 'react';
import { checkAuthStatus } from '@/utils/auth-utils';

/**
 * AuthProvider component that checks authentication status on app load
 * and updates the Redux store accordingly
 */
export default function AuthProvider({ children }: { children: React.ReactNode }) {
  const dispatch = useAppDispatch();

  useEffect(() => {
    // Check authentication status when the app loads
    const checkAuth = async () => {
      await checkAuthStatus(dispatch);
    };

    checkAuth();
  }, [dispatch]);

  return <>{children}</>;
}
