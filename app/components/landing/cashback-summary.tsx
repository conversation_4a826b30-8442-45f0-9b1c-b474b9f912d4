'use client';
import RightArrow from '../svg/right-arrow';
import WalletAddSVG from '../svg/wallet-add';
import CancelledSVG from '../svg/cancelled';
import PendingSVG from '../svg/pending';
import { UserOverviewResponse } from '@/services/api/data-contracts';
import fetchWrapper from '@/utils/fetch-wrapper';
import { memo, useState, useEffect } from 'react';
import { toast } from 'react-toastify';

const getEarningsOverview = async () => {
  return await fetchWrapper<UserOverviewResponse>(`/api/proxy/users/overview`);
};

const CashbackSummary = () => {
  const [data, setData] = useState<UserOverviewResponse | undefined>(undefined);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await getEarningsOverview();
        setData(result);
      } catch (err) {
        toast.error('Failed to fetch user summary');
        console.log(err);
      }
    };
    fetchData();
  }, []);
  return (
    <div className='w-[85%] md:w-[55%] min-w-[295px] h-[90px] rounded-[10px] absolute top-[70px] left-[50%] -translate-x-[50%] bg-white flex flex-col'>
      <div className='h-[41px] rounded-inherit bg-container text-cashbackEarned rounded-t-[10px] pt-[17px] px-[17px] flex items-start justify-between'>
        <h4 className='text-[#5E50CC] dark:text-white text-[10px] font-medium tracking-[1.19px] leading-normal'>
          Lifetime Cashback Earned
        </h4>
        <h2 className='text-[#5E50CC] dark:text-white text-[12px] font-black -tracking-[0.48px] leading-normal font-nexa'>
          ₹ {data?.totalCashbackEarned.toLocaleString()}
        </h2>
      </div>
      <div className='rounded-b-[10px] text-white relative h-[49px] w-full flex items-center justify-evenly bg-[#8374FC]'>
        <div className='bg-[#FFC554] w-[28px] h-[4px] -rotate-90 absolute -left-[12px] top-[22px] rounded-b-[10px]' />

        <div className='readyToWithdraw flex items-start'>
          <WalletAddSVG className='text-white w-[15px]' />
          <div className='flex items-between flex-col ms-[10px]'>
            <h2 className='text-sm font-black -tracking-[0.56px] leading-none font-nexa'>
              ₹ {data?.readyToWithdraw.toLocaleString()}
            </h2>
            <span className='text-[7px] sm:text-[9px] leading-normal'>
              Ready to withdraw
            </span>
          </div>
        </div>

        <div className='dash w-[1px] h-[25px] bg-[#E8E5FF] shrink-0' />

        <div className='pending flex items-start'>
          <PendingSVG className='w-[15px] text-white' />
          <div className='flex items-between flex-col ms-[10px]'>
            <h2 className='text-sm font-black -tracking-[0.56px] leading-none font-nexa'>
              {data?.totalPendingCount}
            </h2>
            <span className='text-[7px] sm:text-[9px] leading-normal'>
              Pending
            </span>
          </div>
        </div>

        <div className='dash w-[1px] h-[25px] bg-[#E8E5FF] shrink-0' />

        <div className='cancelled flex items-start'>
          <CancelledSVG className='w-[15px] text-white' />
          <div className='flex items-between flex-col ms-[10px]'>
            <h2 className='text-sm font-black -tracking-[0.56px] leading-none font-nexa'>
              {data?.totalCancelledCount}
            </h2>
            <span className='text-[7px] sm:text-[9px] leading-normal'>
              cancelled
            </span>
          </div>
        </div>
        <RightArrow className='text-white w-[12px]' />
      </div>
    </div>
  );
};

export default memo(CashbackSummary);
